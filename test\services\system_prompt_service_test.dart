import 'package:flutter_test/flutter_test.dart';
import 'package:upshift/models/models.dart';
import 'package:upshift/services/system_prompt_service.dart';

void main() {
  group('SystemPromptService', () {
    late SystemPromptService systemPromptService;

    setUp(() {
      systemPromptService = SystemPromptService.instance;
    });

    group('Singleton Pattern', () {
      test('should return the same instance', () {
        // Act
        final instance1 = SystemPromptService.instance;
        final instance2 = SystemPromptService.instance;

        // Assert
        expect(instance1, same(instance2));
      });
    });

    group('Initialization', () {
      test('should handle initialization gracefully', () async {
        // Act & Assert - should not throw
        expect(
          () => systemPromptService.initialize(),
          returnsNormally,
        );
      });
    });

    group('Prompt Template Loading', () {
      test('should return default prompts when not initialized', () async {
        // Act & Assert - should not throw and return default prompts
        expect(
          () => systemPromptService.getChatWithPersonaPrompt(),
          returnsNormally,
        );
        expect(
          () => systemPromptService.getTitleGenerationPrompt(),
          returnsNormally,
        );
        expect(
          () => systemPromptService.getProfileUpdatePrompt(),
          returnsNormally,
        );
      });

      test('should handle free subscription correctly', () async {
        // Arrange
        final freeSubscription = UserSubscription.free();

        // Act & Assert - should not throw
        expect(
          () => systemPromptService.getChatWithPersonaPrompt(
            userSubscription: freeSubscription,
          ),
          returnsNormally,
        );
        expect(
          () => systemPromptService.getTitleGenerationPrompt(
            userSubscription: freeSubscription,
          ),
          returnsNormally,
        );
        expect(
          () => systemPromptService.getProfileUpdatePrompt(
            userSubscription: freeSubscription,
          ),
          returnsNormally,
        );
      });

      test('should handle premium subscription correctly', () async {
        // Arrange
        final premiumSubscription = UserSubscription(
          isActive: true,
          entitlements: [SubscriptionEntitlement.premium],
          lastChecked: DateTime.now(),
        );

        // Act & Assert - should not throw
        expect(
          () => systemPromptService.getChatWithPersonaPrompt(
            userSubscription: premiumSubscription,
          ),
          returnsNormally,
        );
        expect(
          () => systemPromptService.getTitleGenerationPrompt(
            userSubscription: premiumSubscription,
          ),
          returnsNormally,
        );
        expect(
          () => systemPromptService.getProfileUpdatePrompt(
            userSubscription: premiumSubscription,
          ),
          returnsNormally,
        );
      });

      test('should handle null subscription gracefully', () async {
        // Act & Assert - should not throw
        expect(
          () => systemPromptService.getChatWithPersonaPrompt(
            userSubscription: null,
          ),
          returnsNormally,
        );
        expect(
          () => systemPromptService.getTitleGenerationPrompt(
            userSubscription: null,
          ),
          returnsNormally,
        );
        expect(
          () => systemPromptService.getProfileUpdatePrompt(
            userSubscription: null,
          ),
          returnsNormally,
        );
      });
    });

    group('Prompt Content Validation', () {
      test('should return non-empty prompts', () async {
        // Act
        final chatPrompt = await systemPromptService.getChatWithPersonaPrompt();
        final titlePrompt = await systemPromptService.getTitleGenerationPrompt();
        final profilePrompt = await systemPromptService.getProfileUpdatePrompt();

        // Assert
        expect(chatPrompt.isNotEmpty, true);
        expect(titlePrompt.isNotEmpty, true);
        expect(profilePrompt.isNotEmpty, true);
      });

      test('should return valid prompt content', () async {
        // Act
        final chatPrompt = await systemPromptService.getChatWithPersonaPrompt();
        final titlePrompt = await systemPromptService.getTitleGenerationPrompt();
        final profilePrompt = await systemPromptService.getProfileUpdatePrompt();

        // Assert - prompts should contain expected content
        expect(chatPrompt, contains('AI'));
        expect(titlePrompt, contains('title'));
        expect(profilePrompt, contains('profile'));
      });
    });

    group('Cache Management', () {
      test('should handle cache operations gracefully', () {
        // Act & Assert - should not throw
        expect(
          () => systemPromptService.clearCache(),
          returnsNormally,
        );
        expect(
          () => systemPromptService.getCachedPrompts(),
          returnsNormally,
        );

        final cachedPrompts = systemPromptService.getCachedPrompts();
        expect(cachedPrompts, isA<Map<String, String>>());
      });
    });

    group('Error Handling', () {
      test('should handle subscription service errors gracefully', () async {
        // Arrange
        final invalidSubscription = UserSubscription(
          isActive: false,
          entitlements: [],
          lastChecked: DateTime.now(),
        );

        // Act & Assert - should not throw
        expect(
          () => systemPromptService.getChatWithPersonaPrompt(
            userSubscription: invalidSubscription,
          ),
          returnsNormally,
        );
      });
    });

    group('Subscription Tier Logic', () {
      test('should correctly identify free tier users', () async {
        // Arrange
        final freeSubscription = UserSubscription.free();

        // Act
        final chatPrompt = await systemPromptService.getChatWithPersonaPrompt(
          userSubscription: freeSubscription,
        );

        // Assert - should return a prompt
        expect(chatPrompt, isNotEmpty);
        expect(chatPrompt, isA<String>());
      });

      test('should correctly identify premium tier users', () async {
        // Arrange
        final premiumSubscription = UserSubscription(
          isActive: true,
          entitlements: [SubscriptionEntitlement.premium],
          lastChecked: DateTime.now(),
        );

        // Act
        final chatPrompt = await systemPromptService.getChatWithPersonaPrompt(
          userSubscription: premiumSubscription,
        );

        // Assert - should return a prompt
        expect(chatPrompt, isNotEmpty);
        expect(chatPrompt, isA<String>());
      });

      test('should treat inactive subscription as free tier', () async {
        // Arrange
        final inactiveSubscription = UserSubscription(
          isActive: false,
          entitlements: [SubscriptionEntitlement.premium],
          lastChecked: DateTime.now(),
        );

        // Act
        final chatPrompt = await systemPromptService.getChatWithPersonaPrompt(
          userSubscription: inactiveSubscription,
        );

        // Assert - should return a prompt
        expect(chatPrompt, isNotEmpty);
        expect(chatPrompt, isA<String>());
        expect(inactiveSubscription.hasPremiumAccess, false);
      });
    });

    group('API Consistency', () {
      test('should have consistent method signatures', () async {
        // This test ensures all prompt methods follow the same pattern
        final freeSubscription = UserSubscription.free();
        
        // Act - all methods should accept UserSubscription parameter
        expect(
          () => systemPromptService.getChatWithPersonaPrompt(
            userSubscription: freeSubscription,
          ),
          returnsNormally,
        );
        expect(
          () => systemPromptService.getTitleGenerationPrompt(
            userSubscription: freeSubscription,
          ),
          returnsNormally,
        );
        expect(
          () => systemPromptService.getProfileUpdatePrompt(
            userSubscription: freeSubscription,
          ),
          returnsNormally,
        );
      });
    });
  });
}
